package com.yonyou.ucf.mdf.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.yonyou.aipierp.dto.ncapi.UFinterface;
import com.yonyou.aipierp.dto.ncapi.dept.DeptBill;
import com.yonyou.aipierp.dto.ncapi.dept.DeptBillHead;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ucf.mdf.api.IDeptService;
import com.yonyou.ucf.mdf.api.NCOpenApiService;
import com.yonyou.ucf.mdf.repository.AIPORepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;


@Slf4j
@Component
public class DeptServiceImpl implements IDeptService {

    private SimpleDateFormat deptCreateTimeFormat;
    NCOpenApiConfig ncOpenApiConfig;
    NCOpenApiService ncOpenApiService;
    AIPORepository aipoRepository;

    public DeptServiceImpl(NCOpenApiConfig ncOpenApiConfig,
                           NCOpenApiService ncOpenApiService,
                           AIPORepository aipoRepository) {
        this.deptCreateTimeFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        this.ncOpenApiConfig = ncOpenApiConfig;
        this.ncOpenApiService = ncOpenApiService;
        this.aipoRepository = aipoRepository;
    }

    /**
     * 推送部门部门信息到高级版
     *
     * @return {@link JSONObject }
     */
    @Override
    public JSONObject pushDeptInfoToNC(JSONObject bipDeptInfo) {
        UFinterface uFinterface = convertToDeptSaveUFinterface(bipDeptInfo);
        JSONObject resp = ncOpenApiService.addDeptEx(uFinterface);
        if (!resp.getBooleanValue("success") ||
                !"1".equals(resp.getJSONObject("data").getJSONObject("ufinterface").getJSONArray("sendresult").getJSONObject(0).getString("resultcode"))) {
            log.error("同步部门信息至高级版系统失败 uFinterface-->{},resp-->{}", uFinterface, resp);
            throw new BusinessException("同步部门信息至高级版系统失败，错误信息：" + resp.toJSONString());
        }
        return resp;
    }


    /**
     * 根据部门id从高级版删除部门
     *
     * @param deptId 部门 ID
     * @return {@link JSONObject }
     */
    @Override
    public JSONObject deleteDeptFromNC(String deptId) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", deptId);
        jsonObject.put("version", "1");
        JSONObject resp = ncOpenApiService.deleteDeptByBipId(jsonObject);
        if (!resp.getBooleanValue("success")) {
            log.error("同步部门信息从高级版删除失败 req-->{},resp-->{}", jsonObject, resp);
            throw new BusinessException("同步部门信息从高级版删除失败，错误信息：" + resp.toJSONString());
        }
        return resp;
    }

    private UFinterface convertToDeptSaveUFinterface(JSONObject bipDeptInfo){
        String id = bipDeptInfo.getString("id");
        JSONObject dept = aipoRepository.getDeptInfoById(id);

        UFinterface deptUFinterface = UFinterface.init(ncOpenApiConfig.getBiz_center(),"dept",ncOpenApiConfig.getGroupcode());
        DeptBillHead deptBillHead = new DeptBillHead();
        deptBillHead.setCode(bipDeptInfo.getString("code"));
        // 多语的名称
        JSONObject mlNameMap = bipDeptInfo.getJSONObject("name");
        if (mlNameMap !=null){
            deptBillHead.setName(mlNameMap.getString("zh_CN"));
        }
        // 多语的简称
        JSONObject mlShortNameMap = bipDeptInfo.getJSONObject("shortname");
        if (mlShortNameMap!=null){
            deptBillHead.setShortname(mlShortNameMap.getString("zh_CN"));
        }
        deptBillHead.setPk_group(ncOpenApiConfig.getGroupcode());
        // 判断上级部门id
        String parent = bipDeptInfo.getString("parent");   // 上级组织部门id
        // 组织部门saverule List 返回的return参数中parentorgid的值根本不对，从数据库里能查到数据就一定一定从数据库里取这个值
        String parentorgid = bipDeptInfo.getString("parentorgid"); // 上级组织id
        if (dept != null) {
            parentorgid = dept.getString("parentorgid");
        }
        // 设置上级部门
        if (parent!=null && parent.equals(parentorgid)){
            // 上级组织 == 上级组织部门，此部门为顶级部门
            deptBillHead.setPk_fatherorg(null);
        }else {
            deptBillHead.setPk_fatherorg(parent);
        }
        deptBillHead.setPk_org(parentorgid);   // 上级组织
        deptBillHead.setDepttype("0");  // 普通部门
        // 启用停用
        String deptEnable = bipDeptInfo.getString("enable");
        if ("0".equals(deptEnable)){
            deptBillHead.setEnablestate("1");
        }else if ("1".equals(deptEnable)){
            deptBillHead.setEnablestate("2");
        }else if ("2".equals(deptEnable)){
            deptBillHead.setEnablestate("3");
        }
        if (bipDeptInfo.getDate("creationtime")!=null){
            deptBillHead.setCreatedate(deptCreateTimeFormat.format(bipDeptInfo.getDate("creationtime")));
        }
        deptBillHead.setHrcanceled("N");
        deptBillHead.setDisplayorder(bipDeptInfo.getString("sort") == null ? "1" : bipDeptInfo.getString("sort"));
        deptBillHead.setAddress("");
        deptBillHead.setOrgtype13("Y");
        deptBillHead.setOrgtype17("N");
        deptBillHead.setTel(bipDeptInfo.getString("telephone"));
        deptBillHead.setMemo("");

        DeptBill deptBill = DeptBill.init(deptBillHead,bipDeptInfo.getString("id"));
        deptUFinterface.setBill(deptBill);
        return deptUFinterface;
    }
}
