package com.yonyou.ucf.mdf.rule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yonyou.ucf.mdf.api.IDeptService;
import com.yonyou.ypd.bill.basic.vo.RulCtxVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.yonyou.ucf.mdd.common.model.rule.RuleExecuteResult;
import com.yonyou.ucf.mdd.ext.exceptions.BusinessException;
import com.yonyou.ypd.bill.basic.service.api.IYpdCommonRul;

import java.util.Map;

@Slf4j
@Component("aipoDeptAfterDeleteRule")
public class AIPODeptAfterDeleteRule implements IYpdCommonRul {

    @Autowired
    IDeptService deptService;

    @Override
    public Object execute(RulCtxVO rulCtxVO, Map<String, Object> params) {
        RuleExecuteResult result = new RuleExecuteResult();

        JSONObject deleteParams = JSON.parseObject(JSON.toJSONString(params));
        if (MapUtils.isEmpty(deleteParams.getJSONObject("requestData"))){
            log.error("无法获取到部门删除的详细信息已删除高级版部门，请重试 deleteParams-->{}",deleteParams);
            throw new BusinessException("无法获取到部门删除的详细信息已删除高级版部门，请重试");
        }else {
            String id = deleteParams.getJSONObject("requestData").getString("id");
            deptService.deleteDeptFromNC(id);
        }

        return result;
    }
}
